import React, { useState, useEffect, useCallback } from 'react'
import { View, Flex, Text, Alert, Spinner } from '@instructure/ui'
import StudentsTable from './StudentsTable'
import StudentSearchInput from './SelectStudent'
import CreateObserverLinkModal from './CreateObserverLinkModal'
import StudentCalendar from './StudentCalendar'
import debounce from '@instructure/debounce'
import * as API from './../../utils/api'

const STUDENTS_PER_PAGE = 25

const StudentsList = ({ showSearch = false }) => {
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState('users.sortable_name')
  const [ascending, setAscending] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)

  const [totalStudents, setTotalStudents] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  const [students, setStudents] = useState([])

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)

  // Observer link creation state
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState(null)
  const [actionLoading, setActionLoading] = useState(false)
  const [createdObserverLink, setCreatedObserverLink] = useState(null)
  const [observerLinkError, setObserverLinkError] = useState(null)
  const [expiredLinkAlert, setExpiredLinkAlert] = useState(false)

  // Initial loading state to prevent flash
  const [initialLoading, setInitialLoading] = useState(true)

  const handleSearchChange = (query) => {
    setSearchQuery(query)
    // Reset to first page when searching
    setCurrentPage(1)
  }

  // Debounced search function
  const debouncedFetchStudents = useCallback(
    debounce(async (query, page, sortBy, ascending) => {
      setLoading(true)
      setIsSearching(!!query && query.length >= 3)

      try {
        const params = {
          per_page: STUDENTS_PER_PAGE,
          page: page,
          sort_by: sortBy,
          sort_order: ascending ? 'ASC' : 'DESC'
        }

        // Add search parameter if query is provided and meets minimum length
        if (query && query.length >= 3) {
          params.search = query
        }

        const response = await API.getStudents(params)
        const data = response.data
        setStudents(data.users)
        setTotalStudents(data.pagination.total_items)
        setTotalPages(data.pagination.total_pages)
      } catch (error) {
        console.error('Error fetching students:', error)
      } finally {
        setLoading(false)
      }
    }, 500),
    []
  )

  // Check for existing observer link on component mount
  useEffect(() => {
    const checkExistingObserverLink = async () => {
      try {
        const response = await API.getCurrentObserverLink()
        if (response.data.data) {
          setCreatedObserverLink(response.data.data)
        }
      } catch (error) {
        if (error.response?.status === 404) {
          setObserverLinkError(
            error.response.data.error || 'No active observer link found'
          )
        }
      } finally {
        setInitialLoading(false)
      }
    }

    checkExistingObserverLink()
  }, [])

  // Fetch students when dependencies change (only after initial check is complete)
  useEffect(() => {
    if (!initialLoading) {
      debouncedFetchStudents(searchQuery, currentPage, sortBy, ascending)
    }
  }, [
    currentPage,
    sortBy,
    ascending,
    searchQuery,
    debouncedFetchStudents,
    initialLoading
  ])

  const handleSort = (column) => {
    if (sortBy === column) {
      setAscending(!ascending)
    } else {
      setSortBy(column)
      setAscending(true)
    }
  }

  const handleCreateObserverLink = (student) => {
    setSelectedStudent(student)
    setShowCreateModal(true)
  }

  const handleConfirmCreateLink = async (student) => {
    setActionLoading(true)
    try {
      const response = await API.createObserverLink(student.canvas_id)
      const observerLink = response.data.data // Assuming API returns the created link
      setCreatedObserverLink(observerLink)
      setShowCreateModal(false)
      setSelectedStudent(null)
    } catch (error) {
      console.error('Error creating observer link:', error)
      // Re-throw the error so the modal can handle it
      throw error
    } finally {
      setActionLoading(false)
    }
  }

  const handleEndLink = async () => {
    try {
      await API.endObserverLink(createdObserverLink.id, {
        org_id: createdObserverLink.organization_id
      })
      setCreatedObserverLink(null)
    } catch (error) {
      console.error('Error ending observer link:', error)
    }
  }

  const handleRenewLink = async () => {
    try {
      const response = await API.renewObserverLink(createdObserverLink.id, {
        org_id: createdObserverLink.organization_id
      })
      setCreatedObserverLink(response.data.data)
    } catch (error) {
      console.error('Error renewing observer link:', error)
    }
  }

  const handleLinkExpired = () => {
    setExpiredLinkAlert(true)
    setCreatedObserverLink(null)
  }

  // Show loading spinner during initial check
  if (initialLoading) {
    return (
      <View padding="large" textAlign="center">
        <Spinner renderTitle="Loading..." size="large" />
      </View>
    )
  }

  // If observer link is created, show the student calendar
  if (createdObserverLink) {
    return (
      <View>
        <StudentCalendar
          observerLink={createdObserverLink}
          onEndLink={handleEndLink}
          onRenewLink={handleRenewLink}
          onExpired={handleLinkExpired}
          loading={actionLoading}
        />
      </View>
    )
  }

  return (
    <View>
      <Flex justifyItems="space-between" margin="0 0 large 0">
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">
            Student Observer Tool
          </Text>
        </Flex.Item>
      </Flex>

      {observerLinkError && (
        <Alert
          variant="info"
          margin="0 0 medium 0"
          renderCloseButtonLabel="Close"
          timeout={5000}
          onDismiss={() => setObserverLinkError(null)}
        >
          {observerLinkError}
        </Alert>
      )}

      {expiredLinkAlert && (
        <Alert
          variant="warning"
          margin="0 0 medium 0"
          renderCloseButtonLabel="Close"
          timeout={8000}
          onDismiss={() => setExpiredLinkAlert(false)}
        >
          <Text weight="bold">Observer Link Expired</Text>
          <br />
          Your current observer link has expired. Please create a new link to
          continue observing a student.
        </Alert>
      )}

      <Flex
        justifyItems="space-between"
        alignItems="center"
        padding="none 0 medium 0"
      >
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">
            Students
          </Text>
        </Flex.Item>

        {showSearch && (
          <Flex.Item>
            <StudentSearchInput
              onSearchChange={handleSearchChange}
              searchValue={searchQuery}
            />
          </Flex.Item>
        )}
      </Flex>

      <StudentsTable
        students={students}
        onCreateObserverLink={handleCreateObserverLink}
        loading={loading}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        sortBy={sortBy}
        ascending={ascending}
        handleSort={handleSort}
        isFiltered={isSearching}
        totalResults={totalStudents}
        searchQuery={searchQuery}
      />

      <CreateObserverLinkModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          setSelectedStudent(null)
        }}
        student={selectedStudent}
        onConfirm={handleConfirmCreateLink}
        loading={actionLoading}
      />
    </View>
  )
}

export default StudentsList
