import React from 'react'
import { View, Flex, Text, Alert } from '@instructure/ui'
import { IconClockLine, IconUserLine } from '@instructure/ui-icons'
import { formatTimeRemaining, getStatusVariant } from '../../utils/timeUtils'
import { useCountdownTimer } from './CountdownTimer'
import ObserverLinkFooter from './ObserverLinkFooter'

const StudentCalendar = ({
  observerLink,
  onEndLink,
  onRenewLink,
  onExpired,
  loading = false
}) => {
  const timeRemaining = useCountdownTimer(observerLink?.expires_at, onExpired)

  if (!observerLink) return null

  return (
    <View>
      <Flex justifyItems="space-between" margin="0 0 large 0">
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">
            Student Observer Tool
          </Text>
        </Flex.Item>
      </Flex>

      {timeRemaining <= 120 && (
        <Alert
          variant={getStatusVariant(timeRemaining)}
          margin="0 0 large 0"
          renderCloseButtonLabel="Close"
          timeout={5000}
        >
          <Flex direction="column" gap="small">
            <Text weight="bold">
              {timeRemaining <= 0
                ? 'Observer Link Expired'
                : 'Active Observer Link'}
            </Text>
            <Flex alignItems="center" gap="small">
              <IconClockLine />
              <Text>Time remaining: {formatTimeRemaining(timeRemaining)}</Text>
            </Flex>
          </Flex>
        </Alert>
      )}

      <View
        as="div"
        background="primary"
        padding="medium"
        borderRadius="medium"
        margin="0 0 large 0"
      >
        <Flex direction="column" gap="medium">
          <Text size="large" weight="bold" color="primary">
            Currently Observing
          </Text>

          <Flex alignItems="center" gap="small">
            <IconUserLine />
            <Flex direction="column" gap="x-small">
              <Text weight="bold">
                {observerLink.observed_student.sortable_name}
              </Text>
              <Text size="small">
                SIS ID: {observerLink.observed_student.sis_id || 'N/A'}
              </Text>
            </Flex>
          </Flex>
        </Flex>
      </View>

      <ObserverLinkFooter
        observerLink={observerLink}
        timeRemaining={timeRemaining}
        onEndLink={onEndLink}
        onRenewLink={onRenewLink}
        loading={loading}
      />
    </View>
  )
}

export default StudentCalendar
