# frozen_string_literal: true

class Api::V1::ObserverLinksController < ApplicationController
  def current
    authorize! :read, StudentObserverLink

    @observer_link = nil
    current_user.against_shards do |shard_user, org|
      existing_link = StudentObserverLink.active.for_observer(shard_user.canvas_id).first
      if existing_link
        @observer_link = existing_link
        @observer_link.class_eval do
          define_method(:org_id) { org.id }
        end
        break
      end
    end

    if @observer_link
      render json: {
        data: format_observer_link(@observer_link)
      }, status: :ok
    else
      render json: { data: nil }, status: :ok
    end
  end

  def renew
    authorize! :update, StudentObserverLink

    PandaPal::Organization.find(params[:org_id]).switch_tenant do
      @observer_link = StudentObserverLink.active.find(params[:id])
      if @observer_link.renew!
        @observer_link.class_eval do
          define_method(:org_id) { current_organization.id }
        end
        render json: {
          data: format_observer_link(@observer_link),
          message: 'Observer link renewed successfully'
        }, status: :ok
      else
        render json: {
          error: 'This link cannot be renewed or has already been renewed.'
        }, status: :unprocessable_entity
      end
    end
  end

  def end_link
    authorize! :update, StudentObserverLink

    PandaPal::Organization.find(params[:org_id]).switch_tenant do
      @observer_link = StudentObserverLink.active.find(params[:id])

      @observer_link&.end_link!

      render json: {
        message: 'Observer link ended successfully'
      }, status: :ok
    end
  end

  private

  def format_observer_link(observer_link)
    {
      organization_id: observer_link.org_id,
      id: observer_link.id,
      observer_user_id: observer_link.observer_user_id,
      observed_student: {
        canvas_id: observer_link.observed_student.canvas_id,
        sortable_name: observer_link.observed_student.sortable_name,
        sis_id: observer_link.observed_student.pseudonyms.first&.sis_id
      },
      created_at: observer_link.created_at,
      expires_at: observer_link.expires_at,
      renewed_at: observer_link.renewed_at,
      time_remaining_minutes: observer_link.time_remaining,
      can_be_renewed: observer_link.can_be_renewed?,
      status: observer_link.status
    }
  end
end
