# frozen_string_literal: true

class Api::V1::ObserverLinksController < ApplicationController
  before_action :validate_params, except: [:current]

  def current
    authorize! :read, StudentObserverLink

    @observer_link = nil
    @observer_link_data = nil
    current_user.against_shards do |shard_user, org|
      existing_link = StudentObserverLink.active.for_observer(shard_user.canvas_id).first
      if existing_link
        @observer_link = existing_link
        assign_org_id(@observer_link, org.id)
        @observer_link_data = format_observer_link(@observer_link)
        break
      end
    end

    render json: { data: @observer_link_data }, status: :ok
  end

  def renew
    authorize! :update, StudentObserverLink

    find_organization.switch_tenant do
      @observer_link = find_active_observer_link

      if @observer_link.renew!
        assign_org_id(@observer_link, current_organization.id)
        render json: {
          data: format_observer_link(@observer_link),
          message: 'Observer link renewed successfully'
        }, status: :ok
      else
        render json: { error: 'This link cannot be renewed.' }, status: :unprocessable_entity
      end
    end
  end

  def end_link
    authorize! :update, StudentObserverLink

    find_organization.switch_tenant do
      @observer_link = find_active_observer_link

      if @observer_link&.end_link!
        render json: { message: 'Observer link ended successfully' }, status: :ok
      else
        render json: { error: 'Failed to end observer link' }, status: :unprocessable_entity
      end
    end
  end

  private

  def validate_params
    render json: { error: 'Organization ID is required' }, status: :bad_request if params[:org_id].blank?
    render json: { error: 'Observer link ID is required' }, status: :bad_request if params[:id].blank?
  end

  def find_organization
    @find_organization ||= PandaPal::Organization.find(params[:org_id])
  end

  def find_active_observer_link
    # Get the current user's canvas_id for the current organization's shard
    shard_user_canvas_id = current_user_canvas_id_for_current_org
    StudentObserverLink.active.for_observer(shard_user_canvas_id).find(params[:id])
  rescue ActiveRecord::RecordNotFound
    # Override the default error message with a more user-friendly one
    raise ActiveRecord::RecordNotFound, 'Active Observer link not found'
  end

  def current_user_canvas_id_for_current_org
    # Find the user's canvas_id in the current organization's shard
    current_user.against_shards do |shard_user, org|
      return shard_user.canvas_id if org.id == find_organization.id
    end
    # Fallback to the base canvas_id if not found in any shard
    current_user.canvas_id
  end

  def assign_org_id(observer_link, org_id)
    observer_link.define_singleton_method(:org_id) { org_id }
  end

  def format_observer_link(observer_link)
    {
      organization_id: observer_link.org_id,
      id: observer_link.id,
      observer_user_id: observer_link.observer_user_id,
      observed_student: {
        canvas_id: observer_link.observed_student.canvas_id,
        sortable_name: observer_link.observed_student.sortable_name,
        sis_id: observer_link.observed_student.pseudonyms.first&.sis_id
      },
      created_at: observer_link.created_at,
      expires_at: observer_link.expires_at,
      renewed_at: observer_link.renewed_at,
      time_remaining_minutes: observer_link.time_remaining,
      can_be_renewed: observer_link.can_be_renewed?,
      status: observer_link.status
    }
  end
end
